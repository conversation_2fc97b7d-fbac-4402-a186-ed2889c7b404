<?php

namespace Webkul\Product\Repositories;

use Illuminate\Support\Facades\Storage;
use Webkul\Core\Eloquent\Repository;
use Illuminate\Support\Str;
use Intervention\Image\ImageManager;

class ProductAttributeValueRepository extends Repository
{
    /**
     * Specify Model class name
     */
    public function model(): string
    {
        return 'Webkul\Product\Contracts\ProductAttributeValue';
    }

    /**
     * Save attribute values
     *
     * @param  array  $data
     * @param  \Webkul\Product\Contracts\Product  $product
     * @param  mixed  $attributes
     * @return void
     */
    public function saveValues($data, $product, $attributes)
    {
        $attributeValuesToInsert = [];

        foreach ($attributes as $attribute) {
            if ($attribute->type === 'boolean') {
                $data[$attribute->code] = ! empty($data[$attribute->code]);
            }

            if (in_array($attribute->type, ['multiselect', 'checkbox'])) {
                // 确保值是数组格式，如果不是则处理为数组
                $value = $data[$attribute->code] ?? [];
                
                if (is_string($value)) {
                    // 如果已经是字符串，可能是逗号分隔的值，检查是否需要转换
                    $value = empty($value) ? [] : explode(',', $value);
                } elseif (!is_array($value)) {
                    // 如果不是数组也不是字符串，转换为数组
                    $value = empty($value) ? [] : [$value];
                }
                
                // 过滤空值并重新索引数组
                $value = array_filter($value, function($item) {
                    return !empty($item) && $item !== '';
                });
                
                $data[$attribute->code] = implode(',', $value);
            }

            if ($attribute->type === 'multiimage') {
                $paths = [];
                
                // Get existing images
                $existingImages = [];
                if ($attributeValue = $product->attribute_values->where('attribute_id', $attribute->id)->first()) {
                    $existingImages = json_decode($attributeValue->json_value, true) ?? [];
                }
                
                if (isset($data[$attribute->code]['files']) && is_array($data[$attribute->code]['files'])) {
                    foreach ($data[$attribute->code]['files'] as $key => $file) {
                        if ($file instanceof \Illuminate\Http\UploadedFile) {
                            // New file upload - process like product images
                            $manager = new ImageManager;
                            $image = $manager->make($file)->encode('webp');
                            $path = 'product/' . $product->id . '/' . Str::random(40) . '.webp';
                            Storage::put($path, $image);
                            $paths[] = $path;
                        } elseif (is_string($key) && in_array($key, $existingImages)) {
                            // Keep existing image
                            $paths[] = $key;
                        }
                    }
                }
                
                // Delete removed images
                foreach ($existingImages as $existingPath) {
                    if (!in_array($existingPath, $paths)) {
                        Storage::delete($existingPath);
                    }
                }
                
                $data[$attribute->code] = $paths;
            }

            if (! isset($data[$attribute->code])) {
                continue;
            }

            if (
                $attribute->type === 'price'
                && empty($data[$attribute->code])
            ) {
                $data[$attribute->code] = null;
            }

            if (
                $attribute->type === 'date'
                && empty($data[$attribute->code])
            ) {
                $data[$attribute->code] = null;
            }

            if (in_array($attribute->type, ['image', 'file'])) {
                $data[$attribute->code] = gettype($data[$attribute->code]) === 'object'
                    ? request()->file($attribute->code)->store('product/'.$product->id)
                    : $data[$attribute->code];
            }

            $attributeValues = $product->attribute_values
                ->where('attribute_id', $attribute->id);

            $channel = $attribute->value_per_channel ? ($data['channel'] ?? core()->getDefaultChannelCode()) : null;

            $locale = $attribute->value_per_locale ? ($data['locale'] ?? core()->getDefaultLocaleCodeFromDefaultChannel()) : null;

            if ($attribute->value_per_channel) {
                if ($attribute->value_per_locale) {
                    $filteredAttributeValues = $attributeValues
                        ->where('channel', $channel)
                        ->where('locale', $locale);
                } else {
                    $filteredAttributeValues = $attributeValues
                        ->where('channel', $channel);
                }
            } else {
                if ($attribute->value_per_locale) {
                    $filteredAttributeValues = $attributeValues
                        ->where('locale', $locale);
                } else {
                    $filteredAttributeValues = $attributeValues;
                }
            }

            $attributeValue = $filteredAttributeValues->first();

            $uniqueId = implode('|', array_filter([
                $channel,
                $locale,
                $product->id,
                $attribute->id,
            ]));

            if (! $attributeValue) {
                $attributeValuesToInsert[] = array_merge($this->getAttributeTypeColumnValues($attribute, $data[$attribute->code]), [
                    'product_id'   => $product->id,
                    'attribute_id' => $attribute->id,
                    'channel'      => $channel,
                    'locale'       => $locale,
                    'unique_id'    => $uniqueId,
                ]);
            } else {
                $previousTextValue = $attributeValue->text_value;

                if (in_array($attribute->type, ['image', 'file'])) {
                    /**
                     * If $data[$attribute->code]['delete'] is not empty, that means someone selected the "delete" option.
                     */
                    if (! empty($data[$attribute->code]['delete'])) {
                        Storage::delete($previousTextValue);

                        $data[$attribute->code] = null;
                    }
                    /**
                     * If $data[$attribute->code] is not equal to the previous one, that means someone has
                     * updated the file or image. In that case, we will remove the previous file.
                     */
                    elseif (
                        ! empty($previousTextValue)
                        && $data[$attribute->code] != $previousTextValue
                    ) {
                        Storage::delete($previousTextValue);
                    }
                }

                $attributeValue = $this->update([
                    $attribute->column_name => $data[$attribute->code],
                    'unique_id'             => $uniqueId,
                ], $attributeValue->id);
            }
        }

        if (! empty($attributeValuesToInsert)) {
            $this->insert($attributeValuesToInsert);
        }
    }

    /**
     * @param  mixed  $attribute
     * @param  mixed  $value
     * @return array
     */
    public function getAttributeTypeColumnValues($attribute, $value)
    {
        $attributeTypeFields = array_fill_keys(array_values($attribute->attributeTypeFields), null);

        // 对于某些特殊类型，确保值的格式正确
        if ($attribute->type === 'multiimage' && is_array($value)) {
            // multiimage 类型应该存储为 JSON
            $value = json_encode($value);
        } elseif (in_array($attribute->type, ['multiselect', 'checkbox']) && is_array($value)) {
            // 如果 multiselect 或 checkbox 仍然是数组，转换为字符串
            $value = implode(',', $value);
        }

        $attributeTypeFields[$attribute->column_name] = $value;

        return $attributeTypeFields;
    }

    /**
     * @param  string  $column
     * @param  int  $attributeId
     * @param  int  $productId
     * @param  string  $value
     * @return bool
     */
    public function isValueUnique($productId, $attributeId, $column, $value)
    {
        $count = $this->resetScope()
            ->model
            ->where($column, $value)
            ->where('attribute_id', '=', $attributeId)
            ->where('product_id', '!=', $productId)
            ->count('id');

        return ! $count;
    }
}
